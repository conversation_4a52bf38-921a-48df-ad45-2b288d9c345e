import React, { useContext } from 'react';
import { View, Text } from 'react-native';
import { getStyles } from './styles';
import BackBtn from 'componentsV2/common/BackBtn';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAtomValue } from 'jotai';
import headerThemeAtom from './theme';
import { useNavigation } from '@react-navigation/native';
import { NativeInfoContext } from 'contextV2/nativeInfoContext';
import { waterInfoAtom } from 'pages/DrinkWater/store';
import navigationService from '../../../services/navigationService';

export default function Header() {
  const theme = useAtomValue(headerThemeAtom);
  const styles = getStyles(theme);
  const insets = useSafeAreaInsets();
  const paddingTop = 10 + insets.top;
  const navigation = useNavigation();
  const nativeInfo = useContext(NativeInfoContext);
  const hideHeader = nativeInfo.embed === '1';
  const waterInfo = useAtomValue(waterInfoAtom);

  // 自定义返回处理函数
  const handleBackPress = () => {
    // 如果是通过 route=drinkWater 进入的，使用自定义返回逻辑
    if (nativeInfo?.route === 'drinkWater') {
      navigationService.goBackFromDrinkWater();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.container, { paddingTop }, hideHeader ? { opacity: 0 } : null]}>
      <View style={[styles.backBtn, { top: paddingTop }]}>
        {hideHeader ? null : <BackBtn onPress={handleBackPress}/>}
      </View>
      <Text style={styles.title}>{waterInfo?.title || '健康喝水打卡'}</Text>
    </View>
  );
}
