import React, { useEffect, useCallback } from 'react'
import { View, Text, TouchableOpacity, Image } from 'react-native'
import { useAtomValue, useSetAtom, useAtom } from 'jotai'
import watchAd from 'utils/watchAd'
import { Toast } from '@xmly/rn-sdk'
import { getStyles } from './styles'
import { shareConinsTaskAtom, writeShareConinsTaskAtom } from './store'
import { pendingRewardModalAtom } from 'atom/listenTaskModal'
import nativeInfoModule from '../../../modulesV2/nativeInfoModule'
import { changeShareCoinsStatusAtom } from 'atom/shareCoinsModal'
import { FallbackReqType } from 'constants/ad'
import { LISTEN_TASK_POSITION, AD_SOURCE, RewardType, NonCoinRewardType } from 'constants/ad'
import { ShareCoinsTaskStatus } from 'services/welfare/shareCoinsTask'
import { RewardModalInfo } from 'atom/listenTaskModal'
import xmlog from 'utilsV2/xmlog'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import useCalendar from '../../CashTask/hook/useCalendar'
import ModuleCard from '../common/ModuleCard'
import shareCoinsThemeAtom from './theme'
import ProgressWindow from './ProgressWindow'
import FireworksAnimation from './ FireworksAnimation'

const titleIcon = 'https://imagev2.xmcdn.com/storages/058c-audiofreehighqps/E6/77/GAqhqKwL-H70AAAD6gOt2pBS.png'

const ShareCoinsTask = () => {
  const theme = useAtomValue(shareCoinsThemeAtom)
  const shareConinsTaskInfo = useAtomValue(shareConinsTaskAtom)
  const fetchShareCoinsTask = useSetAtom(writeShareConinsTaskAtom)
  const { added, addToCalendar } = useCalendar()
  const styles = getStyles(theme)
  const rewardGoldCoin = useRewardGoldCoin()
  const setPendingRewardModal = useSetAtom(pendingRewardModalAtom);

  const [isCollapse, changeShareCoinsStatus] = useAtom(changeShareCoinsStatusAtom)

  const taskFailed = shareConinsTaskInfo.taskStatus === ShareCoinsTaskStatus.FAILED && !shareConinsTaskInfo.replenishSignInBtnText

  useEffect(() => {
    fetchShareCoinsTask()
    receiveAward()
  }, [])

  const clickReport = useCallback(function clickReport(metaId: number, btnText: string) {
    // 福利中心-瓜分模块  点击事件
    try {
      xmlog.click(metaId, undefined, { currPage: 'welfareCenter', status: '展开', area: 'button', Item: btnText })
    } catch (e) {
      console.warn('福利中心-瓜分模块  点击事件失败')
    }
  }, [])

  useEffect(() => {
    if (
      added &&
      !shareConinsTaskInfo.enableReward &&
      shareConinsTaskInfo.alreadyTimes === shareConinsTaskInfo.totalTimes &&
      shareConinsTaskInfo.replenishSignInAlreadyTimes === shareConinsTaskInfo.replenishSignInTotalTimes
    ) {
      changeShareCoinsStatus(true)
    } else {
      changeShareCoinsStatus(false)
    }
  }, [
    added,
    shareConinsTaskInfo.enableReward,
    changeShareCoinsStatus,
    shareConinsTaskInfo.alreadyTimes,
    shareConinsTaskInfo.totalTimes,
    shareConinsTaskInfo.replenishSignInAlreadyTimes,
    shareConinsTaskInfo.replenishSignInTotalTimes,
  ])

  /**
   * 观看广告并领取奖励金币
   *
   * 1. 调用 watchAd 拉起广告，等待用户观看完成。
   * 2. 广告观看成功后，调用 rewardGoldCoin 领取奖励金币。
   * 3. 如果领取成功，刷新任务数据，否则弹出失败提示。
   * 4. 捕获并处理所有异常，确保用户有失败提示。
   * @returns {Promise<void>}
   */
  const handleWatchAd = async () => {
    try {
      const res = await watchAd({
        sourceName: AD_SOURCE.SHARE_COINS,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.SHARE_COINS,
        coins: 0,
        rewardVideoStyle: 0,
      })
      if (res.success) {
        const result = await rewardGoldCoin({
          rewardType: RewardType.SHARE_COINS,
          sourceName: AD_SOURCE.SHARE_COINS,
          coins: 0, //  自己传
          adId: res.adId, //  广告给
          adResponseId: res.adResponseId, // 广告给
          encryptType: res.encryptType, // 广告给
          ecpm: res.ecpm, // 广告给
          fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL, //广告给
        })
        if (result?.success) {
          fetchShareCoinsTask()
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  const parseText = (text: string) => {
    const parts = text.split(/(\{\{.*?\}\})/)
    return parts.map((part, index) => {
      if (part.match(/\{\{.*?\}\}/)) {
        const emphasizedText = part.replace(/\{\{|\}\}/g, '')
        return (
          <Text
            key={index}
            style={styles.bold}
          >
            {emphasizedText}
          </Text>
        )
      }
      return <Text key={index}>{part}</Text>
    })
  }

  const receiveAward = async () => {
    try {
      const nativeInfo = nativeInfoModule.getInfo()
      const result = await rewardGoldCoin(
        {
          rewardType: RewardType.RECEIVE_SHARE_COINS,
          sourceName: AD_SOURCE.SHARE_COINS,
          coins: 0,
          adId: 0,
          adResponseId: 0,
          encryptType: '',
          ecpm: '',
          fallbackReq: 0,
        },
        true,
        true
      )
      if (result?.success) {
        fetchShareCoinsTask()
        const modalInfo: RewardModalInfo = {
          showRewardModal: true,
          coins: result?.coins,
          upgradeCoins: result?.upgradeCoins,
          modalType: 'normal',
          title: "恭喜你",
          subTitle: "在本轮瓜分中获得",
        }
        if(!nativeInfo?.srcChannel?.includes('ListenTask')) {
          setPendingRewardModal(modalInfo)
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  const handleComplement = async () => {
    try {
      const res = await watchAd({
        sourceName: AD_SOURCE.SHARE_COINS,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.SHARE_COINS,
        coins: 0,
        rewardVideoStyle: 0,
      })
      if (res.success) {
        const result = await rewardGoldCoin(
          {
            rewardType: NonCoinRewardType.SHARE_COINS,
            sourceName: AD_SOURCE.SHARE_COINS,
            coins: 0,
            adId: res.adId,
            adResponseId: res.adResponseId,
            encryptType: res.encryptType,
            ecpm: res.ecpm,
            fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL,
            isNoCoin: true,
          },
          true
        )
        if (result?.success) {
          fetchShareCoinsTask()
        } else {
          Toast.info('获取奖励失败')
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  const renderTitleIcon = () => (
    <Image
      source={{ uri: titleIcon }}
      style={{ width: 35, height: 35, position: 'absolute', top: -20, left: '40%' }}
    />
  )

  const renderHeaderTitle = () => {
    if (isCollapse) {
      return (
        <View style={[styles.headerTitle, styles.foldedHeaderTitle]}>
          <View>
            <Text style={[styles.titleBold, styles.foldedTitle]}>瓜分百亿金币大奖</Text>
            <Text style={[styles.subTitle, { textAlign: 'left' }]}>{parseText(shareConinsTaskInfo.subTitle)}</Text>
          </View>
          <Text style={styles.nextText}>明日再来</Text>
        </View>
      )
    }
    return (
      <View style={[styles.headerTitle, { position: 'relative' }]}>
        {renderTitleIcon()}
        <Text style={styles.titleBold}>
          本周连续7天
          <Text style={styles.titleHighlight}>百亿金币</Text>
          等你来瓜分
        </Text>
        <Text style={styles.subTitle}>{parseText(shareConinsTaskInfo.subTitle)}</Text>
      </View>
    )
  }

  if (isCollapse) return null
  if (!shareConinsTaskInfo.success) return null

  return (
    <ModuleCard>
      <View style={styles.container}>
        {/* 顶部标题 */}
        {renderHeaderTitle()}
        {/* 进度条 */}
        <ProgressWindow
          taskFailed={taskFailed}
          shareConinsTaskInfo={shareConinsTaskInfo}
          onComplementClick={() => {
            handleComplement()
            clickReport(68290, '进度条补签' + shareConinsTaskInfo.replenishSignInBtnText || '')
          }}
          onSignClick={() => {
            clickReport(68290, '进度条签到' + shareConinsTaskInfo.buttonText)
            handleWatchAd()
          }}
        />
        {/* 广告奖励按钮 */}
        {!isCollapse && (
          <>
            {(shareConinsTaskInfo.replenishSignInAlreadyTimes !== shareConinsTaskInfo.replenishSignInTotalTimes ||
              shareConinsTaskInfo.alreadyTimes !== shareConinsTaskInfo.totalTimes) && (
                <View style={styles.adBtnRow}>
                  {/* 左侧按钮：看广告补签 */}
                  {!!shareConinsTaskInfo.replenishSignInBtnText && shareConinsTaskInfo.replenishSignInAlreadyTimes !== shareConinsTaskInfo.replenishSignInTotalTimes && (
                    <TouchableOpacity
                      style={[styles.adBtn, styles.adBtnLeft]}
                      activeOpacity={0.8}
                      onPress={() => {
                        handleComplement()
                        clickReport(68290, shareConinsTaskInfo.replenishSignInBtnText)
                      }}
                    >
                      <Text style={styles.adBtnTextLeft}>{shareConinsTaskInfo.replenishSignInBtnText}</Text>
                    </TouchableOpacity>
                  )}

                  {/* 右侧按钮：看广告领金币 */}
                  <TouchableOpacity
                    style={[
                      styles.adBtn,
                      styles.adBtnRight,
                      { backgroundColor: shareConinsTaskInfo.alreadyTimes === shareConinsTaskInfo.totalTimes ? 'rgba(255, 68, 68, 0.3)' : '#FF4444' },
                    ]}
                    activeOpacity={0.8}
                    onPress={() => {
                      if (shareConinsTaskInfo.alreadyTimes === shareConinsTaskInfo.totalTimes) return
                      handleWatchAd()
                      clickReport(68290, shareConinsTaskInfo.buttonText)
                    }}
                  >
                    <Text style={styles.adBtnTextRight}>{shareConinsTaskInfo.buttonText}</Text>
                  </TouchableOpacity>
                </View>
              )}
            {shareConinsTaskInfo.enableReward && shareConinsTaskInfo.alreadyTimes === shareConinsTaskInfo.totalTimes && (
              <View style={styles.adBtnRow}>
                <FireworksAnimation
                  style={{ position: 'absolute', zIndex: 1000 }}
                  loop
                />
                <TouchableOpacity
                  style={[styles.adBtn, styles.adBtnRight]}
                  activeOpacity={0.8}
                  onPress={() => {
                    handleWatchAd()
                    clickReport(68292, shareConinsTaskInfo.buttonText)
                  }}
                >
                  <Text style={styles.adBtnTextRight}>{shareConinsTaskInfo.buttonText}</Text>
                </TouchableOpacity>
              </View>
            )}
            {!shareConinsTaskInfo.enableReward && shareConinsTaskInfo.replenishSignInTotalTimes === shareConinsTaskInfo.replenishSignInAlreadyTimes && (
              <View style={[styles.adBtnRow, styles.fireworksContainer]}>
                <TouchableOpacity
                  style={[styles.adBtn, styles.adBtnRight]}
                  activeOpacity={0.8}
                  onPress={() => {
                    addToCalendar()
                    clickReport(68292, '今日已领取，开启明天日历提醒')
                  }}
                >
                  <Text style={styles.adBtnTextRight}>今日已领取，开启明天日历提醒</Text>
                </TouchableOpacity>
              </View>
            )}
          </>
        )}
      </View>
    </ModuleCard>
  )
}

export default ShareCoinsTask
