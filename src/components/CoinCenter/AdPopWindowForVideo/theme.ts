import { themeAtom } from '../../../atom/theme';
import icon_rn_close_light from '../../../appImagesV2/icon_rn_close_light'
import icon_rn_close_dark from '../../../appImagesV2/icon_rn_close_dark'
import { atom } from 'jotai';

export const darkTheme = {
  dateTextColor: 'rgba(255, 255, 255, .5)',
  titleColor: '#FFFFFF',
  bottomTextColor: 'rgba(255, 255, 255, .5)',
  contentBgColor: '#1F1F1F',
  todayTextColor: '#FFFFFF',
  dayTextColor: 'rgba(255, 255, 255, .5)',
  shadowGradientColors: ['#1F1F1F', 'rgba(31, 31, 31, 0)'],
  shadowGradientLocations: [0.3, .8],
  unSignIncIcon: 'https://imagev2.xmcdn.com/storages/0a99-audiofreehighqps/DE/23/GKwRIJELwpPUAAAgMwONWNst.png',
  backdateIcon: 'https://imagev2.xmcdn.com/storages/2db2-audiofreehighqps/D2/90/GKwRIUELwpPOAAAapgONWNH9.png',
  progressLineColor: 'rgba(228, 228, 228, .1)',
  shadow: 'https://imagev2.xmcdn.com/storages/8cbf-audiofreehighqps/97/B5/GAqh4zILwpPHAAACxwONWMlB.png',
  iconClose: icon_rn_close_dark,
  // background: linear-gradient(180deg, #2F2525 0%, #1F1F1F 100%)
  headGradientColors: ['#2F2525', '#1F1F1F'],
}

const lightTheme = {
  dateTextColor: 'rgba(36, 0, 0, .5)',
  titleColor: '#240000',
  bottomTextColor: 'rgba(36, 0, 0, .5)',
  contentBgColor: '#FFFFFF',
  todayTextColor: '#240000',
  dayTextColor: 'rgba(36, 0, 0, .5)',
  shadowGradientColors: ['#FBF9FA', 'rgba(251, 249, 250, 0)'],
  shadowGradientLocations: [0, 1],
  detailIcon: 'https://imagev2.xmcdn.com/storages/8da2-audiofreehighqps/67/5A/GAqhp50LsCbhAAAA4QOBbY_x.png',
  unSignIncIcon: 'https://imagev2.xmcdn.com/storages/a99d-audiofreehighqps/69/97/GAqh_aQLwpPUAAAhEwONWNpN.png',
  backdateIcon: 'https://imagev2.xmcdn.com/storages/18c5-audiofreehighqps/5C/E6/GKwRIaILwpPOAAAYZAONWNJm.png',
  progressLineColor: '#E4E4E4',
  shadow: 'https://imagev2.xmcdn.com/storages/5b15-audiofreehighqps/A0/13/GAqh9sALwpPHAAACXwONWMms.png',
  iconClose: icon_rn_close_light,
  // background: linear-gradient(180deg, #FBF1F2 0%, #FFFFFF 100%);
  headGradientColors: ['#FBF1F2', '#FFFFFF'],
}

const signInThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default signInThemeStyleAtom;