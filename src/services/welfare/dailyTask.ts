import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from '../../servicesV2/request';

export enum DailyTaskType {
  RED_PACKET_RAIN = 'RED_PACKET_RAIN',
  TREASURE_BOX = 'TREASURE_BOX',
  PLAY_LET = 'PLAY_LET',
  MARKET = 'MARKET',
  DRINK_WATER = 'DRINK_WATER'
}

export interface DailyTaskItem {
  positionId: number;
  positionName: string;
  title: string;
  subTitle: string;
  coins: number;
  extMap: string;
  calmSeconds: number;
  btnText: string;
  type?: DailyTaskType;
  icon?: string;
  darkIcon?: string;
  maxAmount?: number;
}

interface DailyTaskResult {
  success: boolean;
  code: number;
  list: DailyTaskItem[];
}

export const queryDailyTask = async (): Promise<ResDataType<DailyTaskResult>> => {

  const response = await request<DailyTaskResult>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryDailyTask/ts-${Date.now()}?drinkwater=1`,
  });

  return response as ResDataType<DailyTaskResult>;
}; 