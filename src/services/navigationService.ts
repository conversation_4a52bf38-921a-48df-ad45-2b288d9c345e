import { NavigationContainerRef } from '@react-navigation/native';
import { RootStackParamList } from '../router/type';

class NavigationService {
  private navigationRef: React.RefObject<NavigationContainerRef<RootStackParamList>> | null = null;
  private originalInitialRouteName: keyof RootStackParamList = 'Home';

  setNavigationRef(ref: React.RefObject<NavigationContainerRef<RootStackParamList>>) {
    this.navigationRef = ref;
  }

  setOriginalInitialRouteName(routeName: keyof RootStackParamList) {
    this.originalInitialRouteName = routeName;
  }

  getOriginalInitialRouteName(): keyof RootStackParamList {
    return this.originalInitialRouteName;
  }

  navigate(routeName: keyof RootStackParamList, params?: any) {
    if (this.navigationRef?.current) {
      this.navigationRef.current.navigate(routeName, params);
    }
  }

  goBack() {
    if (this.navigationRef?.current) {
      this.navigationRef.current.goBack();
    }
  }

  replace(routeName: keyof RootStackParamList, params?: any) {
    if (this.navigationRef?.current) {
      this.navigationRef.current.reset({
        index: 0,
        routes: [{ name: routeName, params }],
      });
    }
  }

  // 自定义的从喝水页面返回逻辑
  goBackFromDrinkWater() {
    this.replace(this.originalInitialRouteName);
  }
}

export default new NavigationService();
