import { atom } from 'jotai';

export interface RewardModalInfo {
  showRewardModal: boolean
  coins?: number
  upgradeCoins?: number
  modalType?: 'normal' | 'withAd',
  title?: string | undefined,
  subTitle?: string | undefined
}

const initialState: RewardModalInfo = {
  coins: 0,
  upgradeCoins: 0,
  showRewardModal: false,
  modalType: 'normal',
}
export const showRewardModalAtom = atom<RewardModalInfo>(initialState);
export const pendingRewardModalAtom = atom<RewardModalInfo | null>(null);

export {initialState};
